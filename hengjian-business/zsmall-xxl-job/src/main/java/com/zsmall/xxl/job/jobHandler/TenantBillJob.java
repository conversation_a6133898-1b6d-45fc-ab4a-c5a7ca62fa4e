package com.zsmall.xxl.job.jobHandler;

import cn.hutool.core.date.DateUtil;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.utils.DateUtils;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.mapper.SysTenantMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillDetailsRepairService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Component
@Service
public class TenantBillJob {
    @Resource
    private final SysTenantMapper sysTenantMapper;
    @Resource
    private final BillHeadSupper billHeadSupper;
    @Resource
    private final IBillDetailsRepairService billDetailsRepairService;

    /**
     * 分销商生成账单信息xxlJOb定时任务
     * @return
     */
    @XxlJob("generatedDistributorBill")
    @Transactional
    public void generatedDistributorTenantBillXxlJOB() {
        //分销商每月1号 00:00:00 执行
        int i = DateUtil.dayOfMonth(new Date());
        if (i==1){
            List<String>  tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Distributor.name()));
            for (String tenantId : tenantIDs) {
                billHeadSupper.generatedDistributorBillByTenant(tenantId,true,null,null,null,null,null);
            }
            log.info("分销商生成账单信息xxlJOb定时任务执行成功,时间："+DateUtil.now());
        }
    }

    /**
     * 供应商生成账单信息xxlJOb定时任务
     * @return
     */
    @XxlJob("generatedSupplierBill")
    @Transactional
    public void generatedSupplierTenantBillXxlJOB() {
        int i = DateUtil.dayOfMonth(new Date());
        if (i!=1 && i!=16) {
            return;
        }
        List<String>  tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Supplier.name()));
        for (Object tenantId : tenantIDs) {
            billHeadSupper.generatedSupplierBillByTenant(tenantId,true,null,null,null,null,null);
        }
        log.info("供应商生成账单信息xxlJOb定时任务执行成功,时间："+DateUtil.now());
    }


    /**
     * @description: 生成分销商钱包总览
     * @author: Len
     * @date: 2024/9/23 10:50
     **/
    @XxlJob("generatedTransactionReceipt")
    @Transactional
    public void generatedTransactionReceiptXxlJOB() {
        int i = DateUtil.dayOfMonth(new Date());
        if (i==1){
            List<String>  tenantIDs =  TenantHelper.ignore(()->sysTenantMapper.getAllApprovedTenant(TenantType.Distributor.name()));
            for (Object tenantId : tenantIDs) {
                billHeadSupper.generatedTransactionReceipt(tenantId,true,null,null,null,null,null);
            }
            log.info("分销商钱包总览xxlJOb定时任务执行成功,时间："+DateUtil.now());
        }
    }

    /**
     * 每月5号推送上个月账单到ERP
     */
    @XxlJob("sendBillToErp")
    public void sendBillToErp() {
        String firstDayStartStr = DateUtils.getLastMonthFirstDayStartFormatted();
        String firstDayEndStr = DateUtils.getLastMonthFirstDayEndFormatted();
        billHeadSupper.sendBillToErpByConfirmed(firstDayStartStr,firstDayEndStr,null,null);
    }
    /**
     * 每月5号推送修补表中未推送的全量数据到ERP
     */
    @XxlJob("sendBillRepairToErp")
    public void sendBillRepairDateToErp() {
        try {
            log.info("开始推送修补表中未推送的数据到ERP");
            billDetailsRepairService.pushUnsentRepairDataToErp();
            log.info("修补数据推送任务执行完成");
        } catch (Exception e) {
            log.error("推送修补数据到ERP失败：{}", e.getMessage(), e);
        }
    }

}
adb shell cd /storage/emulated && pwd && ls -la
